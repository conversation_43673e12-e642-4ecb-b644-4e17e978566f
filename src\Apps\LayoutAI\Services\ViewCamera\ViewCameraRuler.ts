import { CategoryName } from "../../Scene3D/NodeName";


// 视角类型
export enum ViewCameraType {
    Default = 0, // 默认
    Bull = 1, // 公牛模式
    Normal = 2, // 普通视角
    Panorama = 3, // 全景视角
}

// 视角规则类型
export enum ViewCameraRulerType {
    SofaView = 1, // 沙发视角

    DiningCabinetView = 2, // 餐边柜视角


    BedView = 3, // 床视角

    BathroomPanoView = 4, // 卫生间全景视角
    StudyroomPanoView = 5,  // 书房全景视角
    TearoomPanoView = 6, // 茶室全景视角
}

// 视角实体真实类型
export enum ViewCameraEntityRealType {
    ViewCamera = "ViewCamera"
}

// 视角配置规则
export interface ViewCameraRuler {
    name: string; // 规则名称
    typeId: ViewCameraRulerType; // 规则类型 ID
    target: CategoryName; // 图元类型，用|分隔，若当前图元不存在则判断下一个
    viewCamera: { // 视角参数
        type: ViewCameraType; // 视角类型
        ukey: string; // 视角标识
        fov?: number; // 视场角
        near?: number; // 近裁面
        far?: number; // 远裁面
        direction?: string; // 方向
        hideNames?: string[]; // 隐藏的图元名称
    };
    pose?: { // 视角姿态
        // 默认为房间/分区中心
        posObj?: String; // 相机位置对象
        // 绝对位置
        x?: number; // 相机位置 X 坐标
        y?: number; // 相机位置 Y 坐标
        z?: number; // 相机位置 Z 坐标
        // 相对位置
        norOffset?: number; // 相机法向偏移量
        gapOffset?: number; // 相机间距偏移量
    }
    condition?: { // 布置的条件，无条件视为 true
        // 优先级：roomId > spaceArea > roomName
        roomId?: string; // 房间 ID ,支持多个空间区域，用|分隔
        spaceArea?: string; // 空间区域,支持多个空间区域，用|分隔
        roomName?: string; // 房间名称,支持多个房间名称，用|分隔
    };
}