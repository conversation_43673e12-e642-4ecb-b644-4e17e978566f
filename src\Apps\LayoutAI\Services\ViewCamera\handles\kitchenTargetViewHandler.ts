import { BaseViewHandler } from "./BaseViewHandler";
import { ViewCameraRuler } from "../ViewCameraRuler";
import {
    TViewCameraEntity,
    IViewCameraGenerateOptions
} from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";

/**
* 对厨房中某个图元生成视角（目标图元根据厨房布局而改变）
*/
export class kitchenTargetViewHandler extends BaseViewHandler {

    handle(ruler: ViewCameraRuler, roomEntity: TRoomEntity, options: IViewCameraGenerateOptions ): TViewCameraEntity[] {
        let entities: TViewCameraEntity[] = [];
        if (this.checkCondition(ruler, roomEntity)) {
            
            
        }
        return entities;
    }

}